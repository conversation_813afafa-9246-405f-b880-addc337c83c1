{"qrgeneratedsuccesfully": "QRCode successfully generated!", "scanqr": "Scan this QR code within the next 40 seconds.", "qrlimitreached": "QRCode generation limit reached, to generate a new QRCode, send the 'init' message again.", "cw.inbox.connected": "🚀 Connection successfully established!", "cw.inbox.disconnect": "🚨 Disconnecting WhatsApp from inbox *{{inboxName}}*.", "cw.inbox.alreadyConnected": "🚨 {{inboxName}} instance is connected.", "cw.inbox.clearCache": "✅ {{inboxName}} instance cache cleared.", "cw.inbox.notFound": "⚠️ {{inboxName}} instance not found.", "cw.inbox.status": "⚠️ {{inboxName}} instance status: *{{state}}*.", "cw.import.startImport": "💬 Starting to import messages. Please wait...", "cw.import.importingMessages": "💬 Importing messages. More one moment...", "cw.import.messagesImported": "💬 {{totalMessagesImported}} messages imported. Refresh page to see the new messages.", "cw.import.messagesException": "💬 Something went wrong in importing messages.", "cw.locationMessage.location": "Location", "cw.locationMessage.latitude": "Latitude", "cw.locationMessage.longitude": "Longitude", "cw.locationMessage.locationName": "Name", "cw.locationMessage.locationAddress": "Address", "cw.locationMessage.locationUrl": "URL", "cw.contactMessage.contact": "Contact", "cw.contactMessage.name": "Name", "cw.contactMessage.number": "Number", "cw.message.notsent": "🚨 The message could not be sent. Please check your connection. {{error}}", "cw.message.numbernotinwhatsapp": "🚨 The message was not sent as the contact is not a valid Whatsapp number.", "cw.message.edited": "Edited Message"}