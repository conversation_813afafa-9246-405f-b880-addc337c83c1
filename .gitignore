# Repo
Baileys
# compiled output
/dist
/node_modules

/Docker/.env

# Logs
logs/**.json
*.log
npm-debug.log*
pnpm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

/docker-compose-data
/docker-data

# Package
/yarn.lock
/pnpm-lock.yaml

# IDEs
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
.nova/*
.idea/*

# Project related
/instances/*
!/instances/.gitkeep
/test/
/src/env.yml
/store
*.env

/temp/*

.DS_Store
*.DS_Store
.tool-versions

/prisma/migrations/*
.clinerules/byterover-rules.md
.kilocode/rules/byterover-rules.md
.roo/rules/byterover-rules.md
.windsurf/rules/byterover-rules.md
.cursor/rules/byterover-rules.mdc
.kiro/steering/byterover-rules.md
.qoder/rules/byterover-rules.md
.augment/rules/byterover-rules.md