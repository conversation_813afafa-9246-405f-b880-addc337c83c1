{"editor.fontSize": 13, "editor.fontLigatures": true, "editor.letterSpacing": 0.5, "editor.smoothScrolling": true, "editor.tabSize": 2, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.fixAll": "explicit"}, "prisma-smart-formatter.typescript.defaultFormatter": "esbenp.prettier-vscode", "prisma-smart-formatter.prisma.defaultFormatter": "Prisma.prisma", "i18n-ally.localesPaths": ["store/messages"]}