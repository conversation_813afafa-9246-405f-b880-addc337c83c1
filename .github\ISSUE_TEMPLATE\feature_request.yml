name: ✨ Feature Request
description: Suggest a new feature or enhancement
title: "[FEATURE] "
labels: ["enhancement", "needs-triage"]
assignees: []

body:
  - type: markdown
    attributes:
      value: |
        Thanks for suggesting a new feature! 
        Please check our [Feature Requests on Canny](https://evolutionapi.canny.io/feature-requests) first.

  - type: textarea
    id: problem
    attributes:
      label: 🎯 Problem Statement
      description: Is your feature request related to a problem? Please describe.
      placeholder: A clear and concise description of what the problem is. Ex. I'm always frustrated when [...]
    validations:
      required: true

  - type: textarea
    id: solution
    attributes:
      label: 💡 Proposed Solution
      description: Describe the solution you'd like
      placeholder: A clear and concise description of what you want to happen.
    validations:
      required: true

  - type: textarea
    id: alternatives
    attributes:
      label: 🔄 Alternatives Considered
      description: Describe alternatives you've considered
      placeholder: A clear and concise description of any alternative solutions or features you've considered.

  - type: dropdown
    id: priority
    attributes:
      label: 📊 Priority
      description: How important is this feature to you?
      options:
        - Low - Nice to have
        - Medium - Would be helpful
        - High - Important for my use case
        - Critical - Blocking my work
    validations:
      required: true

  - type: dropdown
    id: component
    attributes:
      label: 🧩 Component
      description: Which component does this feature relate to?
      options:
        - WhatsApp Integration (Baileys)
        - WhatsApp Business API
        - Chatwoot Integration
        - Typebot Integration
        - OpenAI Integration
        - Dify Integration
        - API Endpoints
        - Database
        - Authentication
        - Webhooks
        - File Storage
        - Other

  - type: textarea
    id: use_case
    attributes:
      label: 🎯 Use Case
      description: Describe your specific use case for this feature
      placeholder: How would you use this feature? What problem does it solve for you?
    validations:
      required: true

  - type: textarea
    id: additional
    attributes:
      label: 📝 Additional Context
      description: Add any other context, screenshots, or examples about the feature request here.
      placeholder: Any additional information, mockups, or examples...
