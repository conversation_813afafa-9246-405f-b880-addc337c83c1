{"name": "evolution-api", "version": "2.3.4", "description": "Rest api for communication with WhatsApp", "main": "./dist/main.js", "type": "commonjs", "scripts": {"build": "tsc --noEmit && tsup", "start": "tsx ./src/main.ts", "start:prod": "node dist/main", "dev:server": "tsx watch ./src/main.ts", "test": "tsx watch ./test/all.test.ts", "lint": "eslint --fix --ext .ts src", "lint:check": "eslint --ext .ts src", "commit": "cz", "commitlint": "commitlint --edit", "db:generate": "node runWithProvider.js \"npx prisma generate --schema ./prisma/DATABASE_PROVIDER-schema.prisma\"", "db:deploy": "node runWithProvider.js \"rm -rf ./prisma/migrations && cp -r ./prisma/DATABASE_PROVIDER-migrations ./prisma/migrations && npx prisma migrate deploy --schema ./prisma/DATABASE_PROVIDER-schema.prisma\"", "db:deploy:win": "node runWithProvider.js \"xcopy /E /I prisma\\DATABASE_PROVIDER-migrations prisma\\migrations && npx prisma migrate deploy --schema prisma\\DATABASE_PROVIDER-schema.prisma\"", "db:studio": "node runWithProvider.js \"npx prisma studio --schema ./prisma/DATABASE_PROVIDER-schema.prisma\"", "db:migrate:dev": "node runWithProvider.js \"rm -rf ./prisma/migrations && cp -r ./prisma/DATABASE_PROVIDER-migrations ./prisma/migrations && npx prisma migrate dev --schema ./prisma/DATABASE_PROVIDER-schema.prisma && cp -r ./prisma/migrations/* ./prisma/DATABASE_PROVIDER-migrations\"", "db:migrate:dev:win": "node runWithProvider.js \"xcopy /E /I prisma\\DATABASE_PROVIDER-migrations prisma\\migrations && npx prisma migrate dev --schema prisma\\DATABASE_PROVIDER-schema.prisma\"", "prepare": "husky"}, "repository": {"type": "git", "url": "git+https://github.com/EvolutionAPI/evolution-api.git"}, "keywords": ["chat", "communication", "message", "send message", "whatsapp", "js-whatsapp", "whatsapp-api", "whatsapp-web", "whatsapp", "whatsapp-chat", "whatsapp-group", "automation", "multi-device", "bot"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/EvolutionAPI/evolution-api/issues"}, "homepage": "https://github.com/EvolutionAPI/evolution-api#readme", "lint-staged": {"src/**/*.{ts,js}": ["eslint --fix"], "src/**/*.ts": ["sh -c 'npm run build'"]}, "config": {"commitizen": {"path": "cz-conventional-changelog"}}, "dependencies": {"@adiwajshing/keyed-db": "^0.2.4", "@aws-sdk/client-sqs": "^3.891.0", "@ffmpeg-installer/ffmpeg": "^1.1.0", "@figuro/chatwoot-sdk": "^1.1.16", "@hapi/boom": "^10.0.1", "@paralleldrive/cuid2": "^2.2.2", "@prisma/client": "^6.16.2", "@sentry/node": "^10.12.0", "@types/uuid": "^10.0.0", "amqplib": "^0.10.5", "audio-decode": "^2.2.3", "axios": "^1.7.9", "baileys": "^7.0.0-rc.3", "class-validator": "^0.14.1", "compression": "^1.7.5", "cors": "^2.8.5", "dayjs": "^1.11.13", "dotenv": "^16.4.7", "emoji-regex": "^10.4.0", "eventemitter2": "^6.4.9", "express": "^4.21.2", "express-async-errors": "^3.1.1", "fluent-ffmpeg": "^2.1.3", "form-data": "^4.0.1", "https-proxy-agent": "^7.0.6", "i18next": "^23.7.19", "jimp": "^1.6.0", "json-schema": "^0.4.0", "jsonschema": "^1.4.1", "jsonwebtoken": "^9.0.2", "kafkajs": "^2.2.4", "link-preview-js": "^3.0.13", "long": "^5.2.3", "mediainfo.js": "^0.3.4", "mime": "^4.0.0", "mime-types": "^2.1.35", "minio": "^8.0.3", "multer": "^2.0.2", "nats": "^2.29.1", "node-cache": "^5.1.2", "node-cron": "^3.0.3", "openai": "^4.77.3", "pg": "^8.13.1", "pino": "^9.10.0", "prisma": "^6.1.0", "pusher": "^5.2.0", "qrcode": "^1.5.4", "qrcode-terminal": "^0.12.0", "redis": "^4.7.0", "rxjs": "^7.8.2", "sharp": "^0.34.2", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "socks-proxy-agent": "^8.0.5", "swagger-ui-express": "^5.0.1", "tsup": "^8.3.5", "uuid": "^13.0.0"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.18", "@types/json-schema": "^7.0.15", "@types/mime": "^4.0.0", "@types/mime-types": "^2.1.4", "@types/node": "^24.5.2", "@types/node-cron": "^3.0.11", "@types/qrcode": "^1.5.5", "@types/qrcode-terminal": "^0.12.2", "@typescript-eslint/eslint-plugin": "^8.44.0", "@typescript-eslint/parser": "^8.44.0", "commitizen": "^4.3.1", "cz-conventional-changelog": "^3.3.0", "eslint": "^8.45.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-simple-import-sort": "^12.1.1", "husky": "^9.1.7", "lint-staged": "^16.1.6", "prettier": "^3.4.2", "tsconfig-paths": "^4.2.0", "tsx": "^4.20.5", "typescript": "^5.7.2"}}