{"version": "1.0", "description": "Cursor IDE configuration for Evolution API project", "rules": {"general": {"max_line_length": 120, "indent_size": 2, "end_of_line": "lf", "charset": "utf-8", "trim_trailing_whitespace": true, "insert_final_newline": true}, "typescript": {"quotes": "single", "semi": true, "trailing_comma": "es5", "bracket_spacing": true, "arrow_parens": "avoid", "print_width": 120, "tab_width": 2, "use_tabs": false, "single_quote": true, "end_of_line": "lf", "strict": true, "no_implicit_any": true, "strict_null_checks": true}, "javascript": {"quotes": "single", "semi": true, "trailing_comma": "es5", "bracket_spacing": true, "arrow_parens": "avoid", "print_width": 120, "tab_width": 2, "use_tabs": false, "single_quote": true, "end_of_line": "lf", "style_guide": "eslint-airbnb"}, "json": {"tab_width": 2, "use_tabs": false, "parser": "json"}, "ignore": {"files": ["node_modules/**", "dist/**", "build/**", ".git/**", "*.min.js", "*.min.css", ".env", ".env.*", ".env.example", "coverage/**", "*.log", "*.lock", "pnpm-lock.yaml", "package-lock.json", "yarn.lock", "log/**", "tmp/**", "instances/**", "public/uploads/**", "*.dump", "*.rdb", "*.mmdb", ".DS_Store", "*.swp", "*.swo", "*.un~", ".jest-cache", ".idea/**", ".vscode/**", ".yalc/**", "yalc.lock", "*.local", "prisma/migrations/**", "prisma/mysql-migrations/**", "prisma/postgresql-migrations/**"]}, "search": {"exclude_patterns": ["**/node_modules/**", "**/dist/**", "**/build/**", "**/.git/**", "**/coverage/**", "**/log/**", "**/tmp/**", "**/instances/**", "**/public/uploads/**", "**/*.min.js", "**/*.min.css", "**/*.log", "**/*.lock", "**/pnpm-lock.yaml", "**/package-lock.json", "**/yarn.lock", "**/*.dump", "**/*.rdb", "**/*.mmdb", "**/.DS_Store", "**/*.swp", "**/*.swo", "**/*.un~", "**/.jest-cache", "**/.idea/**", "**/.vscode/**", "**/.yalc/**", "**/yalc.lock", "**/*.local", "**/prisma/migrations/**", "**/prisma/mysql-migrations/**", "**/prisma/postgresql-migrations/**"]}, "evolution_api": {"project_type": "nodejs_typescript_api", "backend_framework": "express_prisma", "database": ["postgresql", "mysql"], "cache": ["redis", "node_cache"], "queue": ["rabbitmq", "sqs"], "real_time": "socket_io", "file_storage": ["aws_s3", "minio"], "validation": "class_validator", "logging": "pino", "main_directories": {"source": "src/", "api": "src/api/", "controllers": "src/api/controllers/", "services": "src/api/services/", "integrations": "src/api/integrations/", "dto": "src/api/dto/", "types": "src/api/types/", "guards": "src/api/guards/", "routes": "src/api/routes/", "cache": "src/cache/", "config": "src/config/", "utils": "src/utils/", "exceptions": "src/exceptions/", "validate": "src/validate/", "prisma": "prisma/", "tests": "test/", "docs": "docs/"}, "key_patterns": ["whatsapp_integration", "multi_database_support", "instance_management", "event_driven_architecture", "service_layer_pattern", "dto_validation", "webhook_processing", "message_queuing", "real_time_communication", "file_storage_integration"], "whatsapp_integrations": ["baileys", "meta_business_api", "whatsapp_cloud_api"], "external_integrations": ["chatwoot", "typebot", "openai", "dify", "rabbitmq", "sqs", "s3", "minio"]}}}