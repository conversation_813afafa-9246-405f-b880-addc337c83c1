FROM evoapicloud/evolution-api:latest AS base
WORKDIR /evolution

# Copiamos apenas o necessário para recompilar o dist com as mudanças locais
COPY tsconfig.json tsup.config.ts package.json ./
COPY src ./src

# Recompila usando os node_modules já presentes na imagem base
RUN npm run build

# Runtime final: reaproveita a imagem oficial e apenas sobrepõe o dist
FROM evoapicloud/evolution-api:latest AS final
WORKDIR /evolution
COPY --from=base /evolution/dist ./dist

ENV PROMETHEUS_METRICS=true

# Entrada original da imagem oficial já sobe o app em /evolution

