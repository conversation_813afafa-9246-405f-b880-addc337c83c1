{"dashboard": {"id": null, "title": "Evolution API Monitoring", "tags": ["evolution-api", "whatsapp", "monitoring"], "style": "dark", "timezone": "browser", "panels": [{"id": 1, "title": "API Status", "type": "stat", "targets": [{"expr": "up{job=\"evolution-api\"}", "legendFormat": "API Status"}], "fieldConfig": {"defaults": {"mappings": [{"options": {"0": {"text": "DOWN", "color": "red"}, "1": {"text": "UP", "color": "green"}}, "type": "value"}]}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}}, {"id": 2, "title": "Total Instances", "type": "stat", "targets": [{"expr": "evolution_instances_total", "legendFormat": "Total Instances"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}}, {"id": 3, "title": "Instance Status Overview", "type": "piechart", "targets": [{"expr": "sum by (state) (evolution_instance_state)", "legendFormat": "{{ state }}"}], "gridPos": {"h": 9, "w": 12, "x": 0, "y": 8}}, {"id": 4, "title": "Instances by Integration Type", "type": "piechart", "targets": [{"expr": "sum by (integration) (evolution_instance_up)", "legendFormat": "{{ integration }}"}], "gridPos": {"h": 9, "w": 12, "x": 12, "y": 8}}, {"id": 5, "title": "Instance Uptime", "type": "table", "targets": [{"expr": "evolution_instance_up", "format": "table", "instant": true}], "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true, "__name__": true}, "renameByName": {"instance": "Instance Name", "integration": "Integration Type", "Value": "Status"}}}], "fieldConfig": {"overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Status"}, "properties": [{"id": "mappings", "value": [{"options": {"0": {"text": "DOWN", "color": "red"}, "1": {"text": "UP", "color": "green"}}, "type": "value"}]}]}]}, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 17}}, {"id": 6, "title": "Instance Status Timeline", "type": "timeseries", "targets": [{"expr": "evolution_instance_up", "legendFormat": "{{ instance }} ({{ integration }})"}], "fieldConfig": {"defaults": {"custom": {"drawStyle": "line", "lineInterpolation": "stepAfter", "lineWidth": 2, "fillOpacity": 10, "gradientMode": "none", "spanNulls": false, "insertNulls": false, "showPoints": "never", "pointSize": 5, "stacking": {"mode": "none", "group": "A"}, "axisPlacement": "auto", "axisLabel": "", "scaleDistribution": {"type": "linear"}, "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "thresholdsStyle": {"mode": "off"}}, "min": 0, "max": 1}}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 26}}], "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "templating": {"list": []}, "annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "refresh": "30s", "schemaVersion": 27, "version": 0, "links": []}}