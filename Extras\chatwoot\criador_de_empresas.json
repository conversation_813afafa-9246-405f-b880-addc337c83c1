{"name": "[Evolution] C<PERSON>or de Empresas", "nodes": [{"parameters": {"httpMethod": "POST", "path": "c<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options": {}}, "id": "5a47c10a-e43c-4fa5-baad-4b6cc511bfcd", "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [1420, 860], "webhookId": "6fe428e3-1752-453c-9358-abf18b793387"}, {"parameters": {"method": "POST", "url": "={{ $('Info Base').item.json[\"chatwoot_url\"] }}/platform/api/v1/accounts", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "api_access_token", "value": "={{ $('Info Base').item.json[\"api_access_token\"] }}"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "name", "value": "={{ $json.name_company }}"}, {"name": "locale", "value": "pt_BR"}]}, "options": {}}, "id": "8295c119-3a96-424e-9386-43d75f6816f5", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.httpRequest", "typeVersion": 3, "position": [2020, 860]}, {"parameters": {"method": "POST", "url": "={{ $('Info Base').item.json[\"chatwoot_url\"] }}/platform/api/v1/users", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "api_access_token", "value": "={{ $('Info Base').item.json[\"api_access_token\"] }}"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "name", "value": "={{ $('Info Base').item.json.name_admin }}"}, {"name": "email", "value": "={{ $('Info Base').item.json[\"email\"] }}"}, {"name": "password", "value": "={{ $('Info Base').item.json[\"password\"] }}"}]}, "options": {}}, "id": "4fe5007a-3a6b-490a-a446-e45cc168189f", "name": "Cria Usuario", "type": "n8n-nodes-base.httpRequest", "typeVersion": 3, "position": [2220, 860]}, {"parameters": {"method": "POST", "url": "={{ $('Info Base').item.json[\"chatwoot_url\"] }}/platform/api/v1/accounts/{{ $node[\"Cria Conta\"].json[\"id\"] }}/account_users", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "api_access_token", "value": "={{ $('Info Base').item.json[\"api_access_token\"] }}"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "user_id", "value": "={{ $node[\"Cria Usuario\"].json[\"id\"] }}"}, {"name": "role", "value": "administrator"}]}, "options": {}}, "id": "848c55e2-5678-4291-9602-c94d994da95b", "name": "Add Usuario a Conta", "type": "n8n-nodes-base.httpRequest", "typeVersion": 3, "position": [2420, 860]}, {"parameters": {"fromEmail": "={{ $('Info Base').item.json[\"from_email\"] }}", "toEmail": "={{ $('LimpaDados').item.json.email }}", "subject": "=Bem vindo à {{ $('Info Base').item.json[\"organization\"] }}", "text": "=Olá seja bem vindo:\n\nAbaixo segue seus dados de acesso:\n\nURL: {{ $('Info Base').item.json[\"chatwoot_url\"] }}\n\nuser: {{ $('LimpaDados').item.json[\"email\"] }}\n\nSenha: {{ $('LimpaDados').item.json[\"password\"] }}", "options": {}}, "id": "27f3b24f-1cf2-4d0d-a354-ecba066059f6", "name": "Send Email", "type": "n8n-nodes-base.emailSend", "typeVersion": 2, "position": [3220, 860], "credentials": {"smtp": {"id": "6BxluEUV8zrXKoVG", "name": "[Dgcode] SMTP"}}}, {"parameters": {"values": {"string": [{"name": "api_access_token", "value": "CHATWOOT_PLATFORM_TOKEN"}, {"name": "chatwoot_url", "value": "https://CHATWOOT_URL"}, {"name": "n8n_url", "value": "https://N8N_URL"}, {"name": "organization", "value": "ORGANIZATION_NAME"}, {"name": "logo", "value": "ORGANIZATION_LOGO"}, {"name": "from_email", "value": "FROM_EMAIL"}, {"name": "name", "value": "={{ $json.name_company }}"}, {"name": "email", "value": "={{ $json.email }}"}, {"name": "password", "value": "={{ $json.password }}"}, {"name": "name_company", "value": "={{ $json.name_company }}"}]}, "options": {}}, "id": "38b4069d-e51e-4db7-933f-941b1be6d124", "name": "Info Base", "type": "n8n-nodes-base.set", "typeVersion": 2, "position": [1820, 860]}, {"parameters": {"keepOnlySet": true, "values": {"string": [{"name": "name_admin", "value": "={{$node[\"Webhook\"].json[\"body\"][\"messages\"][0][\"content\"].match(/Nome Usuario Administrador: ([^\\n]+)/)[1];}}"}, {"name": "name_company", "value": "={{$node[\"Webhook\"].json[\"body\"][\"messages\"][0][\"content\"].match(/Nome da Empresa: ([^\\n]+)/)[1];}}"}, {"name": "email", "value": "={{$node[\"Webhook\"].json[\"body\"][\"messages\"][0][\"content\"].match(/Email: ([^\\s]+)/)[1];}}"}, {"name": "password", "value": "={{$node[\"Webhook\"].json[\"body\"][\"messages\"][0][\"content\"].match(/Senha: ([^\\s]+)/)[1];}}"}]}, "options": {}}, "id": "28e29e73-aadc-49ca-bd6d-b57ee0160a21", "name": "LimpaDados", "type": "n8n-nodes-base.set", "typeVersion": 2, "position": [1620, 860]}, {"parameters": {"method": "POST", "url": "={{ $('Info Base').item.json[\"chatwoot_url\"] }}/api/v1/accounts/{{ $('Add Usuario a Conta').item.json.account_id }}/contacts/", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "api_access_token", "value": "={{ $('Cria Usuario').item.json.access_token }}"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n    \"inbox_id\": {{ $('Cria Inbox Start').item.json[\"id\"] }},\n    \"name\": \"Bot {{ $('Info Base').item.json[\"organization\"] }}\",\n    \"phone_number\": \"+123456\",\n    \"avatar_url\": \"{{ $('Info Base').item.json[\"logo\"] }}\"\n}", "options": {}}, "id": "bb671443-bdb4-4f56-99af-f0baef246a3e", "name": "Cria Contato Bot", "type": "n8n-nodes-base.httpRequest", "typeVersion": 3, "position": [2820, 860]}, {"parameters": {"method": "POST", "url": "={{ $('Info Base').item.json[\"chatwoot_url\"] }}/api/v1/accounts/{{ $('Add Usuario a Conta').item.json.account_id }}/automation_rules/", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "api_access_token", "value": "={{ $('Cria Usuario').item.json.access_token }}"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n    \"name\": \"Create Inbox {{ $('Info Base').item.json[\"organization\"] }}\",\n    \"description\": \"Create Inbox {{ $('Info Base').item.json[\"organization\"] }}\",\n    \"event_name\": \"message_created\",\n    \"active\": true,\n    \"actions\": \n    [\n        {\n            \"action_name\": \"send_webhook_event\",\n            \"action_params\": [\"{{ $('Info Base').item.json[\"n8n_url\"] }}/webhook/inbox_whatsapp?utoken={{ $('Cria Usuario').item.json.access_token }}&organization={{ $('Info Base').item.json[\"organization\"] }}\"]\n        }\n    ],\n    \"conditions\": \n    [\n        {\n            \"attribute_key\": \"content\",\n            \"filter_operator\": \"contains\",\n            \"query_operator\": \"and\",\n            \"values\": [\"start:\"]\n        },\n        {\n            \"attribute_key\": \"phone_number\",\n            \"filter_operator\": \"equal_to\",\n            \"query_operator\": \"or\",\n            \"values\": [\"+123456\"]\n        },\n        {\n            \"attribute_key\": \"content\",\n            \"filter_operator\": \"contains\",\n            \"query_operator\": \"and\",\n            \"values\": [\"new_instance:\"]\n        },\n        {\n            \"attribute_key\": \"phone_number\",\n            \"filter_operator\": \"equal_to\",\n            \"values\": [\"+123456\"]\n        }\n    ]\n}", "options": {}}, "id": "e016a2af-b212-4e00-a3ff-8cd03530aa06", "name": "Cria Automação", "type": "n8n-nodes-base.httpRequest", "typeVersion": 3, "position": [3020, 860]}, {"parameters": {"method": "POST", "url": "={{ $('Info Base').item.json[\"chatwoot_url\"] }}/api/v1/accounts/{{ $json.account_id }}/inboxes/", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "api_access_token", "value": "={{ $('Cria Usuario').item.json.access_token }}"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n    \"name\": \"Start {{ $('Info Base').item.json[\"organization\"] }}\",\n    \"channel\": {\n        \"type\": \"api\",\n        \"website_url\": \"\"\n    }\n}", "options": {}}, "id": "d3c42148-8920-4c98-a874-eb7113f2dd22", "name": "Cria Inbox Start", "type": "n8n-nodes-base.httpRequest", "typeVersion": 3, "position": [2620, 860]}, {"parameters": {"content": "## Workflow Criador de Empresas\n**Cria Contas (Empresas) e Usuários através de tema**\n**Instruções**\n**No node Info Base, configure as variáveis de seu Chatwoot e N8N**\n**Obs: A variável api_access_token é o token PlatformApp encontrado no acesso ao Super Admin**\n**Tema para criar novas empresa:**\n\nTema Criador de Empresa:\n\nNome Usuario Administrador: Joao Linhares\nNome da Empresa: Oficina Linhates\nEmail: <EMAIL>\nSenha: Mfcd62!!", "height": 304.02684563758396, "width": 1129.7777777777778}, "id": "d07516c0-4c8e-43ab-ba86-c8d063b09be5", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1420, 520]}], "pinData": {}, "connections": {"Webhook": {"main": [[{"node": "LimpaDados", "type": "main", "index": 0}]]}, "Cria Conta": {"main": [[{"node": "Cria Usuario", "type": "main", "index": 0}]]}, "Cria Usuario": {"main": [[{"node": "Add Usuario a Conta", "type": "main", "index": 0}]]}, "Add Usuario a Conta": {"main": [[{"node": "Cria Inbox Start", "type": "main", "index": 0}]]}, "Info Base": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "LimpaDados": {"main": [[{"node": "Info Base", "type": "main", "index": 0}]]}, "Cria Contato Bot": {"main": [[{"node": "Cria Automação", "type": "main", "index": 0}]]}, "Cria Automação": {"main": [[{"node": "Send Email", "type": "main", "index": 0}]]}, "Cria Inbox Start": {"main": [[{"node": "Cria Contato Bot", "type": "main", "index": 0}]]}}, "active": true, "settings": {}, "versionId": "3ffd6d3f-6966-4de4-af8f-1fda464bc1b8", "id": "79R6qQDtfyCwgYjJ", "meta": {"instanceId": "4ff16e963c7f5197d7e99e6239192860914312fea0ce2a9a7fd14d74a0a0e906"}, "tags": []}