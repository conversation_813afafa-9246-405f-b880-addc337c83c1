name: 🐛 Bug Report
description: Report a bug or unexpected behavior
title: "[BUG] "
labels: ["bug", "needs-triage"]
assignees: []

body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to fill out this bug report! 
        Please search existing issues before creating a new one.

  - type: textarea
    id: description
    attributes:
      label: 📋 Bug Description
      description: A clear and concise description of what the bug is.
      placeholder: Describe the bug...
    validations:
      required: true

  - type: textarea
    id: reproduction
    attributes:
      label: 🔄 Steps to Reproduce
      description: Steps to reproduce the behavior
      placeholder: |
        1. Go to '...'
        2. Click on '....'
        3. Scroll down to '....'
        4. See error
    validations:
      required: true

  - type: textarea
    id: expected
    attributes:
      label: ✅ Expected Behavior
      description: A clear and concise description of what you expected to happen.
      placeholder: What should happen?
    validations:
      required: true

  - type: textarea
    id: actual
    attributes:
      label: ❌ Actual Behavior
      description: A clear and concise description of what actually happened.
      placeholder: What actually happened?
    validations:
      required: true

  - type: textarea
    id: environment
    attributes:
      label: 🌍 Environment
      description: Please provide information about your environment
      value: |
        - OS: [e.g. Ubuntu 20.04, Windows 10, macOS 12.0]
        - Node.js version: [e.g. 18.17.0]
        - Evolution API version: [e.g. 2.3.4]
        - Database: [e.g. PostgreSQL 14, MySQL 8.0]
        - Connection type: [e.g. Baileys, WhatsApp Business API]
    validations:
      required: true

  - type: textarea
    id: logs
    attributes:
      label: 📋 Logs
      description: If applicable, add logs to help explain your problem.
      placeholder: Paste relevant logs here...
      render: shell

  - type: textarea
    id: additional
    attributes:
      label: 📝 Additional Context
      description: Add any other context about the problem here.
      placeholder: Any additional information...
