version: '3.3'

services:
  mysql:
    container_name: mysql
    image: percona/percona-server:8.0
    networks:
      - evolution-net
    restart: always
    ports:
      - 3306:3306
    environment:
      - MYSQL_ROOT_PASSWORD=root
      - TZ=America/Bahia
    volumes:
      - mysql_data:/var/lib/mysql
    expose:
      - 3306

volumes:
  mysql_data:


networks:
  evolution-net:
    name: evolution-net
    driver: bridge
